# utils/logger.py
import logging
import os
from datetime import datetime
from typing import Optional

class Logger:
    """日志工具类"""
    
    def __init__(self, name: str, log_file: Optional[str] = None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers(log_file)
    
    def _setup_handlers(self, log_file: Optional[str]):
        """设置日志处理器"""
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器
        if log_file:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
        print(f"[INFO] {message}")
    
    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
        print(f"[WARNING] {message}")
    
    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)
        print(f"[ERROR] {message}")
    
    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)
        print(f"[DEBUG] {message}")
    
    def success(self, message: str):
        """记录成功日志"""
        self.logger.info(f"SUCCESS: {message}")
        print(f"[SUCCESS] {message}")
    
    def step(self, step_name: str, message: str):
        """记录步骤日志"""
        formatted_message = f"[{step_name}] {message}"
        self.logger.info(formatted_message)
        print(f"[STEP] {formatted_message}")

# 创建全局日志实例
def get_logger(name: str) -> Logger:
    """获取日志实例"""
    timestamp = datetime.now().strftime("%Y%m%d")
    log_file = f"logs/automation_{timestamp}.log"
    return Logger(name, log_file)
