# config/settings.py
import os

class Config:
    """系统配置类"""
    
    # Hub Studio配置
    HUB_STUDIO_PATH = "C:\\Program Files\\HubStudio\\HubStudio.exe"
    HUB_STUDIO_KEYWORDS = ['hub studio', 'hubstudio', '指纹浏览器']
    
    # 图像识别配置
    TEMPLATE_THRESHOLD = 0.8
    OCR_CONFIDENCE_THRESHOLD = 60
    
    # 操作间隔时间（秒）
    CLICK_INTERVAL = 2
    WAIT_TIMEOUT = 30
    STARTUP_TIMEOUT = 60
    PAGE_LOAD_TIMEOUT = 30
    
    # 视频文件配置
    VIDEO_EXTENSIONS = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
    MAX_VIDEO_SIZE_MB = 2048  # 2GB
    
    # 模板图片路径
    TEMPLATES_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'templates')
    
    # Hub Studio模板
    HUB_STUDIO_ICON = os.path.join(TEMPLATES_DIR, 'hub_studio', 'hub_studio_icon.png')
    ENVIRONMENT_LIST = os.path.join(TEMPLATES_DIR, 'hub_studio', 'environment_list.png')
    ENVIRONMENT_ITEM = os.path.join(TEMPLATES_DIR, 'hub_studio', 'environment_item.png')
    
    # YouTube模板
    YOUTUBE_STUDIO_LOGO = os.path.join(TEMPLATES_DIR, 'youtube', 'youtube_studio_logo.png')
    CREATE_BUTTON = os.path.join(TEMPLATES_DIR, 'youtube', 'create_button.png')
    UPLOAD_VIDEO = os.path.join(TEMPLATES_DIR, 'youtube', 'upload_video.png')
    SELECT_FILES = os.path.join(TEMPLATES_DIR, 'youtube', 'select_files.png')
    TITLE_FIELD = os.path.join(TEMPLATES_DIR, 'youtube', 'title_field.png')
    DESCRIPTION_FIELD = os.path.join(TEMPLATES_DIR, 'youtube', 'description_field.png')
    PUBLISH_BUTTON = os.path.join(TEMPLATES_DIR, 'youtube', 'publish_button.png')
    
    # 浏览器模板
    ADDRESS_BAR = os.path.join(TEMPLATES_DIR, 'browser', 'address_bar.png')
    NEW_TAB = os.path.join(TEMPLATES_DIR, 'browser', 'new_tab.png')
    
    # YouTube Studio URL
    YOUTUBE_STUDIO_URL = "https://studio.youtube.com"
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "automation.log"
    
    # 重试配置
    MAX_RETRIES = 3
    RETRY_DELAY = 5
    
    @classmethod
    def validate_config(cls):
        """验证配置"""
        errors = []
        
        # 检查Hub Studio路径
        if not os.path.exists(cls.HUB_STUDIO_PATH):
            errors.append(f"Hub Studio路径不存在: {cls.HUB_STUDIO_PATH}")
        
        # 检查模板目录
        if not os.path.exists(cls.TEMPLATES_DIR):
            errors.append(f"模板目录不存在: {cls.TEMPLATES_DIR}")
        
        return errors
    
    @classmethod
    def get_template_path(cls, category: str, template_name: str) -> str:
        """获取模板文件路径"""
        return os.path.join(cls.TEMPLATES_DIR, category, template_name)
