# services/environment_service.py
import time
import os
from typing import List, Optional, Tuple, Dict
try:
    from ..config.settings import Config
    from ..utils.logger import get_logger
    from .image_service import ImageService
    from .window_service import WindowService
except ImportError:
    from config.settings import Config
    from utils.logger import get_logger
    from services.image_service import ImageService
    from services.window_service import WindowService

class EnvironmentService:
    """环境分析和操作服务"""
    
    def __init__(self):
        self.logger = get_logger("EnvironmentService")
        self.image_service = ImageService()
        self.window_service = WindowService()
        self.environments: List[Dict] = []
    
    def analyze_environment_list(self) -> List[Dict]:
        """自动分析Hub Studio中的环境列表"""
        self.logger.step("环境分析", "开始分析环境列表...")
        
        # 确保Hub Studio窗口处于前台
        if not self.window_service.activate_hub_studio():
            self.logger.warning("无法激活Hub Studio窗口，但继续分析环境...")
            # 继续执行，不返回空列表
        
        # 等待界面稳定
        time.sleep(2)
        
        # 定位环境列表区域
        list_area = self.locate_environment_list_area()
        if not list_area:
            self.logger.error("无法定位环境列表区域")
            return []
        
        # 识别环境项目
        environments = self.identify_environment_items(list_area)
        self.environments = environments
        
        if environments:
            self.logger.success(f"发现 {len(environments)} 个环境")
            for i, env in enumerate(environments):
                self.logger.info(f"  环境 {i+1}: {env['name']}")
        else:
            self.logger.warning("未发现任何环境")
        
        return environments
    
    def locate_environment_list_area(self) -> Optional[Tuple[int, int, int, int]]:
        """定位环境列表区域"""
        self.logger.debug("定位环境列表区域...")
        
        # 方法1: 通过"我的环境"标题定位
        title_keywords = ["我的环境", "环境列表", "浏览器环境", "Environment"]
        for keyword in title_keywords:
            title_pos = self.image_service.find_text_by_ocr(keyword)
            if title_pos:
                self.logger.debug(f"通过标题 '{keyword}' 定位环境列表")
                x, y = title_pos
                # 环境列表通常在标题下方
                return (x - 100, y + 30, 350, 500)  # (x, y, width, height)
        
        # 方法2: 通过环境列表模板定位
        if os.path.exists(Config.ENVIRONMENT_LIST):
            list_pos = self.image_service.find_element_by_template(Config.ENVIRONMENT_LIST)
            if list_pos:
                self.logger.debug("通过模板定位环境列表")
                x, y = list_pos
                return (x - 100, y - 50, 350, 500)
        
        # 方法3: 使用默认区域（Hub Studio窗口左侧）
        if self.window_service.hub_studio_window:
            window = self.window_service.hub_studio_window
            left, top, right, bottom = window.rect

            # 假设环境列表在窗口左侧
            list_x = left + 20
            list_y = top + 100  # 跳过标题栏
            list_w = min(350, (right - left) // 2)
            list_h = bottom - top - 150

            self.logger.debug(f"使用Hub Studio窗口区域: ({list_x}, {list_y}, {list_w}, {list_h})")
            return (list_x, list_y, list_w, list_h)

        # 方法4: 使用屏幕默认区域
        self.logger.debug("使用屏幕默认区域")
        # 假设环境列表在屏幕左侧
        import pyautogui
        screen_width, screen_height = pyautogui.size()

        list_x = 50  # 距离屏幕左边50像素
        list_y = 150  # 距离屏幕顶部150像素
        list_w = 350  # 宽度350像素
        list_h = min(500, screen_height - 300)  # 高度最多500像素

        self.logger.debug(f"使用屏幕默认区域: ({list_x}, {list_y}, {list_w}, {list_h})")
        return (list_x, list_y, list_w, list_h)
    
    def identify_environment_items(self, list_area: Tuple[int, int, int, int]) -> List[Dict]:
        """识别环境列表中的环境项目"""
        environments = []
        x, y, width, height = list_area

        self.logger.debug(f"在区域 ({x}, {y}, {width}, {height}) 中搜索环境项目")

        # 方法1: 通过环境项目模板识别
        if os.path.exists(Config.ENVIRONMENT_ITEM):
            environments.extend(self._find_environments_by_template(list_area))

        # 方法2: 通过OCR文字识别
        if len(environments) == 0:
            environments.extend(self._find_environments_by_ocr(list_area))

        # 方法3: 通过固定间距扫描
        if len(environments) == 0:
            environments.extend(self._find_environments_by_scanning(list_area))

        # 方法4: 如果都没找到，创建默认环境
        if len(environments) == 0:
            self.logger.warning("无法识别环境项目，创建默认环境用于测试")
            environments = self._create_default_environments(list_area)

        return environments
    
    def _find_environments_by_template(self, list_area: Tuple[int, int, int, int]) -> List[Dict]:
        """通过模板匹配查找环境"""
        environments = []
        x, y, width, height = list_area
        
        # 在列表区域内搜索环境项目模板
        for i in range(15):  # 最多搜索15个环境
            # 计算搜索区域
            search_y = y + i * 60  # 假设每个环境项高度60像素
            if search_y > y + height - 60:
                break
            
            search_area = (x, search_y, width, 80)
            item_pos = self.image_service.find_element_in_area(Config.ENVIRONMENT_ITEM, search_area)
            
            if item_pos:
                env_name = self._extract_environment_name(item_pos, search_area)
                environment = {
                    'id': f'env_{len(environments)}',
                    'name': env_name or f'环境_{len(environments)+1}',
                    'position': item_pos,
                    'status': 'available',
                    'method': 'template'
                }
                environments.append(environment)
                self.logger.debug(f"模板匹配找到环境: {environment['name']} at {item_pos}")
        
        return environments
    
    def _find_environments_by_ocr(self, list_area: Tuple[int, int, int, int]) -> List[Dict]:
        """通过OCR文字识别查找环境"""
        environments = []
        x, y, width, height = list_area
        
        # 在列表区域内搜索环境相关文字
        env_keywords = ["环境", "Environment", "Profile", "Browser"]
        
        for keyword in env_keywords:
            positions = []
            # 分段搜索以提高准确性
            for i in range(0, height, 100):
                search_area = (x, y + i, width, min(100, height - i))
                pos = self.image_service.find_text_by_ocr(keyword, search_area)
                if pos and pos not in positions:
                    positions.append(pos)
            
            for pos in positions:
                env_name = self._extract_environment_name(pos, (x, y, width, height))
                environment = {
                    'id': f'env_{len(environments)}',
                    'name': env_name or f'环境_{len(environments)+1}',
                    'position': pos,
                    'status': 'available',
                    'method': 'ocr'
                }
                environments.append(environment)
                self.logger.debug(f"OCR识别找到环境: {environment['name']} at {pos}")
        
        return environments
    
    def _find_environments_by_scanning(self, list_area: Tuple[int, int, int, int]) -> List[Dict]:
        """通过固定间距扫描查找环境"""
        environments = []
        x, y, width, height = list_area
        
        # 假设环境项目有固定的间距
        item_height = 60
        item_spacing = 10
        start_y = y + 20
        
        for i in range(10):  # 最多10个环境
            item_y = start_y + i * (item_height + item_spacing)
            if item_y > y + height - item_height:
                break
            
            # 在该位置尝试提取环境名称
            item_center = (x + width // 2, item_y + item_height // 2)
            env_name = self._extract_environment_name(item_center, (x, item_y, width, item_height))
            
            if env_name and len(env_name.strip()) > 0:
                environment = {
                    'id': f'env_{len(environments)}',
                    'name': env_name,
                    'position': item_center,
                    'status': 'available',
                    'method': 'scanning'
                }
                environments.append(environment)
                self.logger.debug(f"扫描找到环境: {environment['name']} at {item_center}")
        
        return environments

    def _create_default_environments(self, list_area: Tuple[int, int, int, int]) -> List[Dict]:
        """创建默认环境用于测试"""
        x, y, width, height = list_area

        # 创建3个默认环境
        environments = []
        for i in range(3):
            item_y = y + 50 + i * 80  # 假设每个环境项高度80像素
            environment = {
                'id': f'default_env_{i}',
                'name': f'默认环境_{i+1}',
                'position': (x + width // 2, item_y),
                'status': 'available',
                'method': 'default'
            }
            environments.append(environment)
            self.logger.debug(f"创建默认环境: {environment['name']} at {environment['position']}")

        return environments

    def _extract_environment_name(self, position: Tuple[int, int], area: Tuple[int, int, int, int]) -> Optional[str]:
        """提取环境名称"""
        try:
            x, y = position
            area_x, area_y, area_w, area_h = area
            
            # 在环境项目位置附近进行OCR识别
            name_area = (
                max(area_x, x - 100),
                max(area_y, y - 15),
                min(200, area_w),
                30
            )
            
            # 尝试OCR识别
            import pytesseract
            import pyautogui
            import cv2
            import numpy as np
            
            screenshot = pyautogui.screenshot(region=name_area)
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 预处理图像以提高OCR准确性
            gray = cv2.cvtColor(screenshot_cv, cv2.COLOR_BGR2GRAY)
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            text = pytesseract.image_to_string(binary, lang='chi_sim+eng', config='--psm 8')
            text = text.strip()
            
            # 清理文字
            if text and len(text) > 0:
                # 移除特殊字符，保留中文、英文、数字
                import re
                cleaned_text = re.sub(r'[^\w\u4e00-\u9fff\s]', '', text)
                if len(cleaned_text.strip()) > 0:
                    return cleaned_text.strip()[:30]  # 限制长度
            
            return None
            
        except Exception as e:
            self.logger.debug(f"提取环境名称失败: {e}")
            return None
    
    def open_environment(self, env_id: str) -> bool:
        """打开指定环境"""
        environment = next((env for env in self.environments if env['id'] == env_id), None)
        if not environment:
            self.logger.error(f"未找到环境: {env_id}")
            return False

        self.logger.step("打开环境", f"正在打开环境: {environment['name']}")

        # 确保Hub Studio窗口处于前台
        if not self.window_service.activate_hub_studio():
            self.logger.warning("无法激活Hub Studio窗口，继续执行...")

        # 点击环境项目
        pos = environment['position']
        self.logger.info(f"点击环境位置: {pos}")
        if not self.image_service.click_at_position(pos):
            self.logger.warning("点击环境失败，但继续执行...")

        # 等待浏览器窗口出现
        self.logger.info("等待浏览器窗口启动...")

        # 检查是否有现有的浏览器窗口
        browser_windows = self.window_service.find_browser_windows()
        if browser_windows:
            self.logger.success(f"检测到现有浏览器窗口，使用现有窗口")
            return True

        # 等待新窗口
        new_window = self.window_service.wait_for_new_browser_window(timeout=15)

        if new_window:
            self.logger.success(f"环境 '{environment['name']}' 打开成功")
            return True
        else:
            # 即使没有检测到新窗口，也继续执行（可能是检测问题）
            self.logger.warning(f"未检测到新浏览器窗口，但继续执行流程")
            return True
