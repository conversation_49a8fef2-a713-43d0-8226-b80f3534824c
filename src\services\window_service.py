# services/window_service.py
import win32gui
import win32con
import win32api
import time
import psutil
from typing import Optional, List, Dict, Tuple
from dataclasses import dataclass
try:
    from ..config.settings import Config
    from ..utils.logger import get_logger
except ImportError:
    from config.settings import Config
    from utils.logger import get_logger

@dataclass
class WindowInfo:
    """窗口信息"""
    hwnd: int
    title: str
    class_name: str
    rect: Tuple[int, int, int, int]  # (left, top, right, bottom)
    process_id: int
    is_visible: bool

class WindowService:
    """窗口管理服务"""
    
    def __init__(self):
        self.logger = get_logger("WindowService")
        self.hub_studio_window: Optional[WindowInfo] = None
        self.browser_windows: List[WindowInfo] = []
        self.initial_windows: List[int] = []
        
        # 记录初始窗口状态
        self._record_initial_windows()
    
    def _record_initial_windows(self):
        """记录初始窗口状态"""
        def enum_windows_proc(hwnd, lParam):
            if win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd):
                self.initial_windows.append(hwnd)
            return True
        
        win32gui.EnumWindows(enum_windows_proc, 0)
        self.logger.debug(f"记录了 {len(self.initial_windows)} 个初始窗口")
    
    def find_hub_studio_window(self) -> bool:
        """查找Hub Studio窗口"""
        try:
            self.logger.info("查找Hub Studio窗口...")

            windows = self._enumerate_all_windows()

            # 扩展关键词列表
            extended_keywords = Config.HUB_STUDIO_KEYWORDS + [
                'hubstudio', 'hub', 'studio', '指纹', '浏览器', 'browser',
                'fingerprint', 'anti', 'detect'
            ]

            for window in windows:
                title_lower = window.title.lower()
                class_lower = window.class_name.lower()

                # 检查是否匹配Hub Studio关键词
                for keyword in extended_keywords:
                    if keyword in title_lower or keyword in class_lower:
                        # 验证窗口大小（Hub Studio通常有一定的最小尺寸）
                        left, top, right, bottom = window.rect
                        width = right - left
                        height = bottom - top

                        if width > 200 and height > 150:  # 降低尺寸要求
                            self.hub_studio_window = window
                            self.logger.success(f"找到Hub Studio窗口: {window.title} ({width}x{height})")
                            return True

            # 如果还是找不到，尝试查找任何包含常见窗口类名的窗口
            for window in windows:
                title_lower = window.title.lower()
                if len(window.title) > 0 and any(char.isalnum() for char in window.title):
                    left, top, right, bottom = window.rect
                    width = right - left
                    height = bottom - top

                    if width > 400 and height > 300:  # 查找较大的窗口
                        self.logger.info(f"尝试使用窗口: {window.title} ({width}x{height})")
                        # 可以手动选择这个窗口

            self.logger.warning("未找到Hub Studio窗口，但继续执行")
            return False

        except Exception as e:
            self.logger.error(f"查找Hub Studio窗口失败: {e}")
            return False
    
    def activate_hub_studio(self) -> bool:
        """激活Hub Studio窗口"""
        if not self.hub_studio_window:
            if not self.find_hub_studio_window():
                return False
        
        return self.activate_window(self.hub_studio_window)
    
    def activate_window(self, window: WindowInfo) -> bool:
        """激活指定窗口"""
        try:
            # 检查窗口是否仍然存在
            if not win32gui.IsWindow(window.hwnd):
                self.logger.warning(f"窗口已不存在: {window.title}")
                return False
            
            # 如果窗口最小化，先恢复
            if win32gui.IsIconic(window.hwnd):
                win32gui.ShowWindow(window.hwnd, win32con.SW_RESTORE)
                time.sleep(0.5)
            
            # 将窗口置于前台
            win32gui.SetForegroundWindow(window.hwnd)
            win32gui.BringWindowToTop(window.hwnd)
            
            # 等待窗口激活
            time.sleep(1)
            
            self.logger.debug(f"已激活窗口: {window.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"激活窗口失败: {window.title} - {e}")
            return False
    
    def wait_for_new_browser_window(self, timeout: int = 30) -> Optional[WindowInfo]:
        """等待新的浏览器窗口出现"""
        self.logger.info("等待新浏览器窗口出现...")
        
        start_time = time.time()
        initial_browser_count = len(self.browser_windows)
        
        while time.time() - start_time < timeout:
            current_browsers = self.find_browser_windows()
            
            # 检查是否有新窗口
            if len(current_browsers) > initial_browser_count:
                # 找到新窗口
                for browser in current_browsers:
                    if browser not in self.browser_windows:
                        self.logger.success(f"检测到新浏览器窗口: {browser.title}")
                        self.browser_windows.append(browser)
                        return browser
            
            time.sleep(1)
        
        self.logger.warning("等待新浏览器窗口超时")
        return None
    
    def find_browser_windows(self) -> List[WindowInfo]:
        """查找所有浏览器窗口"""
        try:
            windows = self._enumerate_all_windows()
            browser_windows = []
            
            # 浏览器窗口特征
            browser_indicators = [
                'chrome', 'firefox', 'edge', 'browser', 'chromium',
                'google', 'mozilla', 'microsoft edge', 'opera'
            ]
            
            for window in windows:
                title_lower = window.title.lower()
                class_lower = window.class_name.lower()
                
                # 检查是否是浏览器窗口
                is_browser = False
                for indicator in browser_indicators:
                    if indicator in title_lower or indicator in class_lower:
                        is_browser = True
                        break
                
                if is_browser:
                    # 验证窗口大小
                    left, top, right, bottom = window.rect
                    width = right - left
                    height = bottom - top
                    
                    if width > 400 and height > 300:
                        browser_windows.append(window)
            
            return browser_windows
            
        except Exception as e:
            self.logger.error(f"查找浏览器窗口失败: {e}")
            return []
    
    def _enumerate_all_windows(self) -> List[WindowInfo]:
        """枚举所有窗口"""
        windows = []
        
        def enum_windows_proc(hwnd, lParam):
            try:
                if win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd):
                    title = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    rect = win32gui.GetWindowRect(hwnd)
                    
                    # 获取进程ID
                    _, process_id = win32gui.GetWindowThreadProcessId(hwnd)
                    
                    # 过滤掉太小的窗口
                    left, top, right, bottom = rect
                    width = right - left
                    height = bottom - top
                    
                    if width > 50 and height > 50 and title.strip():
                        windows.append(WindowInfo(
                            hwnd=hwnd,
                            title=title,
                            class_name=class_name,
                            rect=rect,
                            process_id=process_id,
                            is_visible=True
                        ))
            except Exception:
                pass  # 忽略单个窗口的错误
            return True
        
        win32gui.EnumWindows(enum_windows_proc, 0)
        return windows
    
    def get_active_window(self) -> Optional[WindowInfo]:
        """获取当前活动窗口"""
        try:
            hwnd = win32gui.GetForegroundWindow()
            if hwnd:
                title = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                rect = win32gui.GetWindowRect(hwnd)
                _, process_id = win32gui.GetWindowThreadProcessId(hwnd)
                
                return WindowInfo(
                    hwnd=hwnd,
                    title=title,
                    class_name=class_name,
                    rect=rect,
                    process_id=process_id,
                    is_visible=True
                )
        except Exception as e:
            self.logger.error(f"获取活动窗口失败: {e}")
        
        return None
    
    def close_window(self, window: WindowInfo) -> bool:
        """关闭窗口"""
        try:
            win32gui.PostMessage(window.hwnd, win32con.WM_CLOSE, 0, 0)
            self.logger.info(f"已关闭窗口: {window.title}")
            return True
        except Exception as e:
            self.logger.error(f"关闭窗口失败: {window.title} - {e}")
            return False
