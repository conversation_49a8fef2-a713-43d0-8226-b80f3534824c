# services/browser_service.py
import time
import os
from typing import Optional
try:
    from ..config.settings import Config
    from ..utils.logger import get_logger
    from .image_service import ImageService
    from .window_service import WindowService, WindowInfo
except ImportError:
    from config.settings import Config
    from utils.logger import get_logger
    from services.image_service import ImageService
    from services.window_service import WindowService, WindowInfo

class BrowserService:
    """浏览器控制服务"""
    
    def __init__(self):
        self.logger = get_logger("BrowserService")
        self.image_service = ImageService()
        self.window_service = WindowService()
        self.current_browser: Optional[WindowInfo] = None
    
    def activate_browser(self) -> bool:
        """激活浏览器窗口"""
        # 查找最新的浏览器窗口
        browser_windows = self.window_service.find_browser_windows()

        if not browser_windows:
            self.logger.warning("未找到浏览器窗口，等待3秒后重试...")
            time.sleep(3)
            browser_windows = self.window_service.find_browser_windows()

            if not browser_windows:
                self.logger.warning("仍未找到浏览器窗口，但继续执行...")
                return True  # 继续执行而不是失败
        
        # 使用最后一个浏览器窗口（通常是最新打开的）
        self.current_browser = browser_windows[-1]
        
        if self.window_service.activate_window(self.current_browser):
            self.logger.success(f"已激活浏览器: {self.current_browser.title}")
            return True
        else:
            self.logger.error("激活浏览器窗口失败")
            return False
    
    def navigate_to_url(self, url: str) -> bool:
        """导航到指定URL"""
        try:
            self.logger.info(f"导航到: {url}")
            
            # 确保浏览器窗口处于前台
            if not self.activate_browser():
                return False
            
            # 方法1: 点击地址栏
            if self._click_address_bar():
                # 清空地址栏并输入新URL
                self.image_service.hotkey('ctrl', 'a')
                time.sleep(0.5)
                self.image_service.type_text(url)
                time.sleep(0.5)
                self.image_service.press_key('enter')
                
                self.logger.success(f"已导航到: {url}")
                return True
            
            # 方法2: 使用快捷键
            self.logger.debug("尝试使用快捷键导航")
            self.image_service.hotkey('ctrl', 'l')  # 聚焦地址栏
            time.sleep(0.5)
            self.image_service.type_text(url)
            time.sleep(0.5)
            self.image_service.press_key('enter')
            
            self.logger.success(f"已导航到: {url}")
            return True
            
        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            return False
    
    def _click_address_bar(self) -> bool:
        """点击地址栏"""
        try:
            # 方法1: 通过模板匹配
            if os.path.exists(Config.ADDRESS_BAR):
                address_bar_pos = self.image_service.find_element_by_template(Config.ADDRESS_BAR)
                if address_bar_pos:
                    self.image_service.click_at_position(address_bar_pos)
                    return True
            
            # 方法2: 通过OCR查找地址栏
            url_indicators = ["http", "www", "chrome://", "about:"]
            for indicator in url_indicators:
                pos = self.image_service.find_text_by_ocr(indicator)
                if pos:
                    self.image_service.click_at_position(pos)
                    return True
            
            # 方法3: 点击浏览器窗口顶部区域（地址栏通常在这里）
            if self.current_browser:
                left, top, right, bottom = self.current_browser.rect
                # 地址栏通常在窗口顶部
                address_bar_y = top + 80  # 估计的地址栏位置
                address_bar_x = left + (right - left) // 2  # 窗口中央
                
                self.image_service.click_at_position((address_bar_x, address_bar_y))
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"点击地址栏失败: {e}")
            return False
    
    def wait_for_page_load(self, timeout: int = None) -> bool:
        """等待页面加载完成"""
        timeout = timeout or Config.PAGE_LOAD_TIMEOUT
        self.logger.info(f"等待页面加载完成 (超时: {timeout}秒)...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # 检查页面是否加载完成的指标
            # 1. 检查加载指示器是否消失
            # 2. 检查页面标题是否更新
            # 3. 等待一定时间让页面稳定
            
            time.sleep(2)
            
            # 简单的等待策略：等待5秒让页面稳定
            if time.time() - start_time > 5:
                self.logger.success("页面加载完成")
                return True
        
        self.logger.warning("页面加载超时")
        return True  # 即使超时也返回True，让流程继续
    
    def refresh_page(self) -> bool:
        """刷新页面"""
        try:
            self.logger.info("刷新页面")
            
            if not self.activate_browser():
                return False
            
            self.image_service.press_key('f5')
            time.sleep(1)
            
            return self.wait_for_page_load()
            
        except Exception as e:
            self.logger.error(f"刷新页面失败: {e}")
            return False
    
    def go_back(self) -> bool:
        """后退"""
        try:
            self.logger.info("后退")
            
            if not self.activate_browser():
                return False
            
            self.image_service.hotkey('alt', 'left')
            time.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"后退失败: {e}")
            return False
    
    def go_forward(self) -> bool:
        """前进"""
        try:
            self.logger.info("前进")
            
            if not self.activate_browser():
                return False
            
            self.image_service.hotkey('alt', 'right')
            time.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"前进失败: {e}")
            return False
    
    def open_new_tab(self) -> bool:
        """打开新标签页"""
        try:
            self.logger.info("打开新标签页")
            
            if not self.activate_browser():
                return False
            
            self.image_service.hotkey('ctrl', 't')
            time.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"打开新标签页失败: {e}")
            return False
    
    def close_current_tab(self) -> bool:
        """关闭当前标签页"""
        try:
            self.logger.info("关闭当前标签页")
            
            if not self.activate_browser():
                return False
            
            self.image_service.hotkey('ctrl', 'w')
            time.sleep(1)
            
            return True
            
        except Exception as e:
            self.logger.error(f"关闭标签页失败: {e}")
            return False
    
    def scroll_down(self, times: int = 3) -> bool:
        """向下滚动"""
        try:
            if not self.activate_browser():
                return False
            
            for _ in range(times):
                self.image_service.press_key('pagedown')
                time.sleep(0.5)
            
            return True
            
        except Exception as e:
            self.logger.error(f"滚动失败: {e}")
            return False
    
    def scroll_up(self, times: int = 3) -> bool:
        """向上滚动"""
        try:
            if not self.activate_browser():
                return False
            
            for _ in range(times):
                self.image_service.press_key('pageup')
                time.sleep(0.5)
            
            return True
            
        except Exception as e:
            self.logger.error(f"滚动失败: {e}")
            return False
