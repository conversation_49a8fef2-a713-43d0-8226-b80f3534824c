# services/hub_studio_service.py
import subprocess
import time
import psutil
import os
from typing import Optional
try:
    from ..config.settings import Config
    from ..utils.logger import get_logger
    from .image_service import ImageService
except ImportError:
    from config.settings import Config
    from utils.logger import get_logger
    from services.image_service import ImageService

class HubStudioService:
    """Hub Studio启动和管理服务"""
    
    def __init__(self):
        self.logger = get_logger("HubStudioService")
        self.image_service = ImageService()
        self.process: Optional[subprocess.Popen] = None
        self.hub_studio_path = Config.HUB_STUDIO_PATH
    
    def start_hub_studio(self) -> bool:
        """启动Hub Studio"""
        try:
            # 检查是否已经运行
            if self.is_hub_studio_running():
                self.logger.info("Hub Studio已在运行")
                return self.wait_for_interface_ready()
            
            # 检查Hub Studio路径
            if not os.path.exists(self.hub_studio_path):
                self.logger.error(f"Hub Studio路径不存在: {self.hub_studio_path}")
                return False
            
            self.logger.info("正在启动Hub Studio...")
            
            # 启动Hub Studio
            self.process = subprocess.Popen(
                [self.hub_studio_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待进程启动
            time.sleep(3)
            
            # 检查进程是否成功启动
            if self.process.poll() is not None:
                self.logger.error("Hub Studio进程启动失败")
                return False
            
            # 等待Hub Studio完全启动
            if self.wait_for_hub_studio_process():
                self.logger.success("Hub Studio进程启动成功")
                return self.wait_for_interface_ready()
            else:
                self.logger.error("Hub Studio启动超时")
                return False
                
        except Exception as e:
            self.logger.error(f"启动Hub Studio失败: {e}")
            return False
    
    def is_hub_studio_running(self) -> bool:
        """检查Hub Studio是否在运行"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower() if proc_info['name'] else ''
                    proc_exe = proc_info['exe'].lower() if proc_info['exe'] else ''
                    
                    # 检查进程名或可执行文件路径
                    if ('hubstudio' in proc_name or 
                        'hub studio' in proc_name or
                        'hubstudio' in proc_exe):
                        self.logger.debug(f"找到Hub Studio进程: {proc_info['name']} (PID: {proc_info['pid']})")
                        return True
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查Hub Studio进程失败: {e}")
            return False
    
    def wait_for_hub_studio_process(self, timeout: int = None) -> bool:
        """等待Hub Studio进程启动"""
        timeout = timeout or Config.STARTUP_TIMEOUT
        self.logger.info(f"等待Hub Studio进程启动 (超时: {timeout}秒)...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.is_hub_studio_running():
                return True
            time.sleep(1)
        
        return False
    
    def wait_for_interface_ready(self, timeout: int = None) -> bool:
        """等待Hub Studio界面准备就绪"""
        timeout = timeout or Config.STARTUP_TIMEOUT
        self.logger.info(f"等待Hub Studio界面准备就绪 (超时: {timeout}秒)...")

        start_time = time.time()
        while time.time() - start_time < timeout:
            # 方法1: 检查Hub Studio图标
            if os.path.exists(Config.HUB_STUDIO_ICON):
                icon_pos = self.image_service.find_element_by_template(Config.HUB_STUDIO_ICON)
                if icon_pos:
                    self.logger.success("Hub Studio界面已准备就绪 (通过图标识别)")
                    return True

            # 方法2: 通过OCR查找特征文字
            text_indicators = ["我的环境", "环境列表", "浏览器环境", "指纹浏览器"]
            for text in text_indicators:
                if self.image_service.find_text_by_ocr(text):
                    self.logger.success(f"Hub Studio界面已准备就绪 (通过文字识别: {text})")
                    return True

            # 方法3: 检查环境列表区域
            if os.path.exists(Config.ENVIRONMENT_LIST):
                list_pos = self.image_service.find_element_by_template(Config.ENVIRONMENT_LIST)
                if list_pos:
                    self.logger.success("Hub Studio界面已准备就绪 (通过环境列表识别)")
                    return True

            # 方法4: 如果Hub Studio进程在运行，等待一定时间后认为准备就绪
            if time.time() - start_time > 10:  # 等待10秒后
                if self.is_hub_studio_running():
                    self.logger.info("Hub Studio进程运行中，假设界面已准备就绪")
                    return True

            time.sleep(2)

        # 如果超时但进程仍在运行，也认为准备就绪
        if self.is_hub_studio_running():
            self.logger.warning("等待Hub Studio界面超时，但进程运行中，继续执行")
            return True

        self.logger.warning("等待Hub Studio界面超时")
        return False
    
    def stop_hub_studio(self) -> bool:
        """停止Hub Studio"""
        try:
            self.logger.info("正在停止Hub Studio...")
            
            # 如果有启动的进程，先尝试正常终止
            if self.process and self.process.poll() is None:
                self.process.terminate()
                
                # 等待进程结束
                try:
                    self.process.wait(timeout=10)
                    self.logger.info("Hub Studio进程已正常终止")
                except subprocess.TimeoutExpired:
                    # 强制杀死进程
                    self.process.kill()
                    self.logger.warning("Hub Studio进程被强制终止")
            
            # 查找并终止所有Hub Studio相关进程
            terminated_count = 0
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower() if proc_info['name'] else ''
                    proc_exe = proc_info['exe'].lower() if proc_info['exe'] else ''
                    
                    if ('hubstudio' in proc_name or 
                        'hub studio' in proc_name or
                        'hubstudio' in proc_exe):
                        
                        proc.terminate()
                        terminated_count += 1
                        self.logger.debug(f"终止进程: {proc_info['name']} (PID: {proc_info['pid']})")
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            if terminated_count > 0:
                self.logger.success(f"已终止 {terminated_count} 个Hub Studio进程")
            else:
                self.logger.info("未找到运行中的Hub Studio进程")
            
            return True
            
        except Exception as e:
            self.logger.error(f"停止Hub Studio失败: {e}")
            return False
    
    def restart_hub_studio(self) -> bool:
        """重启Hub Studio"""
        self.logger.info("重启Hub Studio...")
        
        if not self.stop_hub_studio():
            self.logger.error("停止Hub Studio失败")
            return False
        
        # 等待进程完全停止
        time.sleep(3)
        
        return self.start_hub_studio()
    
    def get_hub_studio_version(self) -> Optional[str]:
        """获取Hub Studio版本信息"""
        try:
            if not os.path.exists(self.hub_studio_path):
                return None
            
            # 尝试获取文件版本信息
            import win32api
            info = win32api.GetFileVersionInfo(self.hub_studio_path, "\\")
            ms = info['FileVersionMS']
            ls = info['FileVersionLS']
            version = f"{win32api.HIWORD(ms)}.{win32api.LOWORD(ms)}.{win32api.HIWORD(ls)}.{win32api.LOWORD(ls)}"
            
            self.logger.debug(f"Hub Studio版本: {version}")
            return version
            
        except Exception as e:
            self.logger.debug(f"获取Hub Studio版本失败: {e}")
            return None
