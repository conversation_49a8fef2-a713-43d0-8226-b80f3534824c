# YouTube批量视频上传自动化系统配置文件

# Hub Studio配置
hub_studio:
  # Hub Studio可执行文件路径
  executable_path: "C:\\Program Files\\HubStudio\\HubStudio.exe"

  # 启动超时时间（秒）
  startup_timeout: 30

# 图像识别配置
image_recognition:
  # 模板匹配阈值
  template_threshold: 0.8

  # 截图延迟时间（秒）
  screenshot_delay: 0.5

# 批量处理配置
batch_processing:
  # 每批处理的环境数量
  batch_size: 5

  # 批次间延迟时间（秒）
  batch_delay: 3.0

  # 环境间延迟时间（秒）
  environment_delay: 2.0

# YouTube配置
youtube:
  # YouTube Studio URL
  studio_url: "https://studio.youtube.com/"

  # 上传超时时间（秒）
  upload_timeout: 300

# 重试配置
retry:
  # 最大重试次数
  max_retry_count: 3

  # 重试间隔时间（秒）
  retry_delay: 2.0

# 视频文件配置
video:
  # 视频文件目录
  video_directory: "videos"

  # 支持的视频格式
  supported_formats: [".mp4", ".avi", ".mov", ".mkv"]

# 模板文件配置
templates:
  # 模板文件目录
  template_dir: "templates"

  # 必需的模板文件
  required_templates:
    - "my_environments_title.png"  # "我的环境"标题
    - "upload_button.png"          # YouTube上传按钮
    - "title_field.png"            # 视频标题输入框
    - "description_field.png"      # 视频描述输入框
    - "publish_button.png"         # 发布按钮

# 日志配置
logging:
  # 日志级别
  level: "INFO"

  # 日志文件目录
  log_dir: "logs"

  # 日志文件格式
  format: "%(asctime)s - %(levelname)s - %(message)s"
