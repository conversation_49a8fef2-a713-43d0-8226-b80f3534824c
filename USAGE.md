# Hub Studio 全自动化视频上传系统使用指南

## 🚀 快速开始

### 1. 环境准备

#### 安装Python依赖
```bash
pip install -r requirements.txt
```

#### 安装Tesseract OCR
1. 下载并安装 Tesseract OCR: https://github.com/tesseract-ocr/tesseract
2. 将Tesseract安装目录添加到系统PATH环境变量

### 2. 系统测试

运行系统测试脚本验证环境：
```bash
python test_system.py
```

### 3. 配置Hub Studio

1. 确保Hub Studio已正确安装
2. 修改 `src/config/settings.py` 中的Hub Studio路径：
```python
HUB_STUDIO_PATH = "C:\\Program Files\\HubStudio\\HubStudio.exe"
```

### 4. 准备模板图片

在 `templates/` 目录下准备以下模板图片：

#### Hub Studio模板 (`templates/hub_studio/`)
- `hub_studio_icon.png` - Hub Studio图标
- `environment_list.png` - 环境列表区域
- `environment_item.png` - 环境项目模板

#### YouTube模板 (`templates/youtube/`)
- `youtube_studio_logo.png` - YouTube Studio标志
- `create_button.png` - 创建按钮
- `upload_video.png` - 上传视频选项
- `select_files.png` - 选择文件按钮
- `title_field.png` - 标题输入框
- `description_field.png` - 描述输入框
- `publish_button.png` - 发布按钮

#### 浏览器模板 (`templates/browser/`)
- `address_bar.png` - 地址栏
- `new_tab.png` - 新标签页

### 5. 准备视频文件

将要上传的视频文件放在一个目录中，支持的格式：
- .mp4
- .avi
- .mov
- .mkv
- .wmv
- .flv
- .webm

## 🎯 运行程序

### 基本用法
```bash
python run.py <视频目录路径>
```

### 示例
```bash
# 上传C盘Videos文件夹中的所有视频
python run.py C:\Videos

# 上传D盘MyVideos文件夹中的所有视频
python run.py D:\MyVideos
```

## 📋 自动化流程

程序将自动执行以下步骤：

1. **系统检查** - 验证配置和环境
2. **启动Hub Studio** - 自动启动Hub Studio应用
3. **环境分析** - 识别可用的浏览器环境
4. **文件扫描** - 扫描视频目录中的有效文件
5. **批量上传** - 对每个视频执行：
   - 打开浏览器环境
   - 跳转到YouTube Studio
   - 上传视频文件
   - 填写视频信息
   - 发布视频

## ⚙️ 配置选项

编辑 `src/config/settings.py` 来自定义配置：

```python
class Config:
    # Hub Studio路径
    HUB_STUDIO_PATH = "C:\\Program Files\\HubStudio\\HubStudio.exe"
    
    # 图像识别阈值
    TEMPLATE_THRESHOLD = 0.8
    
    # 操作间隔时间（秒）
    CLICK_INTERVAL = 2
    WAIT_TIMEOUT = 30
    
    # 视频文件限制
    MAX_VIDEO_SIZE_MB = 2048  # 2GB
```

## 🔧 故障排除

### 常见问题

#### 1. Hub Studio启动失败
- 检查Hub Studio路径是否正确
- 确认Hub Studio程序可以正常运行
- 检查是否有权限问题

#### 2. 图像识别失败
- 检查模板图片是否与实际界面匹配
- 调整图像识别阈值 `TEMPLATE_THRESHOLD`
- 确保屏幕分辨率和模板图片匹配

#### 3. 环境识别失败
- 确保Hub Studio界面完全加载
- 检查环境列表是否可见
- 尝试手动点击环境验证可用性

#### 4. YouTube上传失败
- 检查网络连接
- 确认YouTube账号已登录
- 验证视频文件格式和大小

### 调试模式

设置环境变量启用详细日志：
```bash
set LOG_LEVEL=DEBUG
python run.py C:\Videos
```

## 📝 注意事项

### 使用前准备
1. 确保Hub Studio已配置至少一个可用的浏览器环境
2. 确保浏览器环境已登录YouTube账号
3. 建议先在测试环境验证功能

### 使用建议
1. 首次使用建议只上传1-2个视频进行测试
2. 确保视频文件格式符合YouTube要求
3. 建议分批上传，避免触发平台限制
4. 监控程序运行日志，及时处理异常

### 安全提醒
1. 程序会自动操作鼠标和键盘，运行时请勿手动操作
2. 按 Ctrl+C 可以安全停止程序
3. 建议在专用的虚拟机或测试环境中运行

## 📊 日志和监控

程序运行时会生成详细的日志文件：
- 日志文件位置: `logs/automation_YYYYMMDD.log`
- 控制台会显示实时进度
- 每个步骤都有详细的状态反馈

## 🆘 获取帮助

如果遇到问题：
1. 首先运行 `python test_system.py` 检查系统状态
2. 查看日志文件了解详细错误信息
3. 检查配置文件是否正确
4. 确认模板图片是否匹配实际界面
