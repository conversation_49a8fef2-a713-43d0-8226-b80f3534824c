# main.py
import sys
import signal
import os
import time
from typing import List
from config.settings import Config
from utils.logger import get_logger
from services.hub_studio_service import HubStudioService
from services.environment_service import EnvironmentService
from services.youtube_service import YouTubeService

class AutomationController:
    """全自动化控制器"""
    
    def __init__(self):
        self.logger = get_logger("AutomationController")
        self.hub_studio_service = HubStudioService()
        self.environment_service = EnvironmentService()
        self.youtube_service = YouTubeService()
        self.is_running = False
    
    def run_full_automation(self, video_directory: str) -> bool:
        """运行完整自动化流程"""
        self.logger.info("=" * 60)
        self.logger.info("🚀 Hub Studio 全自动化视频上传系统")
        self.logger.info("=" * 60)
        
        try:
            self.is_running = True
            
            # 验证配置
            if not self._validate_setup(video_directory):
                return False
            
            # 阶段1: 启动Hub Studio
            if not self.start_hub_studio():
                return False
            
            # 阶段2: 分析环境列表
            environments = self.analyze_environments()
            if not environments:
                return False
            
            # 阶段3: 获取视频文件列表
            video_files = self.get_video_files(video_directory)
            if not video_files:
                self.logger.error("未找到视频文件")
                return False
            
            # 阶段4: 执行批量上传
            return self.execute_batch_upload(environments, video_files)
            
        except KeyboardInterrupt:
            self.logger.warning("用户中断操作")
            return False
        except Exception as e:
            self.logger.error(f"自动化流程出错: {e}")
            return False
        finally:
            self.is_running = False
    
    def _validate_setup(self, video_directory: str) -> bool:
        """验证设置"""
        self.logger.step("系统检查", "验证系统设置...")
        
        # 检查配置
        config_errors = Config.validate_config()
        if config_errors:
            self.logger.error("配置验证失败:")
            for error in config_errors:
                self.logger.error(f"  - {error}")
            return False
        
        # 检查视频目录
        if not os.path.exists(video_directory):
            self.logger.error(f"视频目录不存在: {video_directory}")
            return False
        
        # 检查模板目录
        if not os.path.exists(Config.TEMPLATES_DIR):
            self.logger.warning(f"模板目录不存在: {Config.TEMPLATES_DIR}")
            self.logger.warning("某些图像识别功能可能无法正常工作")
        
        self.logger.success("系统设置验证通过")
        return True
    
    def start_hub_studio(self) -> bool:
        """启动Hub Studio"""
        self.logger.step("启动应用", "启动Hub Studio...")
        
        if not self.hub_studio_service.start_hub_studio():
            self.logger.error("Hub Studio启动失败")
            return False
        
        self.logger.success("✓ Hub Studio启动成功")
        return True
    
    def analyze_environments(self) -> List[dict]:
        """分析环境列表"""
        self.logger.step("环境分析", "分析Hub Studio环境列表...")
        
        environments = self.environment_service.analyze_environment_list()
        if environments:
            self.logger.success(f"✓ 发现 {len(environments)} 个可用环境")
            for i, env in enumerate(environments):
                self.logger.info(f"  环境 {i+1}: {env['name']} ({env['method']})")
        else:
            self.logger.error("未发现任何可用环境")
        
        return environments
    
    def get_video_files(self, directory: str) -> List[str]:
        """获取视频文件列表"""
        self.logger.step("文件扫描", f"扫描视频目录: {directory}")
        
        video_files = []
        
        try:
            for file in os.listdir(directory):
                file_path = os.path.join(directory, file)
                if os.path.isfile(file_path):
                    # 检查文件扩展名
                    _, ext = os.path.splitext(file.lower())
                    if ext in Config.VIDEO_EXTENSIONS:
                        # 检查文件大小
                        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
                        if file_size_mb <= Config.MAX_VIDEO_SIZE_MB:
                            video_files.append(file_path)
                            self.logger.info(f"  ✓ {file} ({file_size_mb:.1f}MB)")
                        else:
                            self.logger.warning(f"  ✗ {file} 文件过大 ({file_size_mb:.1f}MB)")
            
            self.logger.success(f"找到 {len(video_files)} 个有效视频文件")
            return video_files
            
        except Exception as e:
            self.logger.error(f"扫描视频文件失败: {e}")
            return []
    
    def execute_batch_upload(self, environments: List[dict], video_files: List[str]) -> bool:
        """执行批量上传"""
        self.logger.step("批量上传", f"开始批量上传 {len(video_files)} 个视频...")
        
        success_count = 0
        failed_count = 0
        
        for i, video_file in enumerate(video_files):
            if not self.is_running:
                self.logger.warning("用户中断批量上传")
                break
            
            # 循环使用环境
            env_index = i % len(environments)
            environment = environments[env_index]
            
            self.logger.info("=" * 50)
            self.logger.info(f"📹 上传视频 {i+1}/{len(video_files)}")
            self.logger.info(f"视频: {os.path.basename(video_file)}")
            self.logger.info(f"环境: {environment['name']}")
            self.logger.info("=" * 50)
            
            if self.upload_single_video(environment, video_file):
                success_count += 1
                self.logger.success(f"✓ 视频 {i+1} 上传成功")
            else:
                failed_count += 1
                self.logger.error(f"✗ 视频 {i+1} 上传失败")
            
            # 视频间间隔
            if i < len(video_files) - 1:
                self.logger.info("等待下一个视频...")
                time.sleep(5)
        
        # 输出最终结果
        self.logger.info("=" * 60)
        self.logger.info("📊 批量上传完成")
        self.logger.info(f"成功: {success_count}")
        self.logger.info(f"失败: {failed_count}")
        self.logger.info(f"总计: {len(video_files)}")
        self.logger.info(f"成功率: {success_count/len(video_files)*100:.1f}%")
        self.logger.info("=" * 60)
        
        return success_count > 0
    
    def upload_single_video(self, environment: dict, video_file: str) -> bool:
        """上传单个视频"""
        try:
            # 1. 打开环境
            self.logger.step("环境启动", f"打开环境: {environment['name']}")
            if not self.environment_service.open_environment(environment['id']):
                return False
            
            # 2. 跳转到YouTube Studio
            self.logger.step("页面导航", "跳转到YouTube Studio")
            if not self.youtube_service.navigate_to_youtube_studio():
                return False
            
            # 3. 开始上传流程
            self.logger.step("上传启动", "启动视频上传流程")
            if not self.youtube_service.start_video_upload():
                return False
            
            # 4. 选择视频文件
            self.logger.step("文件选择", "选择视频文件")
            if not self.youtube_service.select_video_file(video_file):
                return False
            
            # 5. 填写视频信息
            video_title = os.path.splitext(os.path.basename(video_file))[0]
            self.logger.step("信息填写", f"填写视频信息: {video_title}")
            if not self.youtube_service.fill_video_info(video_title):
                return False
            
            # 6. 发布视频
            self.logger.step("视频发布", "发布视频")
            if not self.youtube_service.publish_video():
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"上传视频时出错: {e}")
            return False

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n收到停止信号，正在安全退出...")
    sys.exit(0)

def print_usage():
    """打印使用说明"""
    print("Hub Studio 全自动化视频上传系统")
    print()
    print("使用方法:")
    print("  python main.py <视频目录路径>")
    print()
    print("示例:")
    print("  python main.py C:\\Videos")
    print("  python main.py D:\\MyVideos")
    print()
    print("注意事项:")
    print("  1. 确保Hub Studio已正确安装")
    print("  2. 确保浏览器环境已配置并登录YouTube")
    print("  3. 视频文件格式支持: .mp4, .avi, .mov, .mkv, .wmv")
    print("  4. 按 Ctrl+C 可以安全停止程序")

def main():
    """主函数"""
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 检查命令行参数
    if len(sys.argv) != 2:
        print_usage()
        sys.exit(1)
    
    video_directory = sys.argv[1]
    
    # 创建自动化控制器
    controller = AutomationController()
    
    # 运行完整自动化流程
    success = controller.run_full_automation(video_directory)
    
    if success:
        print("\n🎉 自动化流程执行完成!")
        sys.exit(0)
    else:
        print("\n❌ 自动化流程执行失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
