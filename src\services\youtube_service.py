# services/youtube_service.py
import time
import os
from typing import Optional
try:
    from ..config.settings import Config
    from ..utils.logger import get_logger
    from .image_service import ImageService
    from .browser_service import BrowserService
except ImportError:
    from config.settings import Config
    from utils.logger import get_logger
    from services.image_service import ImageService
    from services.browser_service import BrowserService

class YouTubeService:
    """YouTube Studio操作服务"""
    
    def __init__(self):
        self.logger = get_logger("YouTubeService")
        self.image_service = ImageService()
        self.browser_service = BrowserService()
    
    def navigate_to_youtube_studio(self) -> bool:
        """导航到YouTube Studio"""
        self.logger.step("YouTube导航", "正在跳转到YouTube Studio...")
        
        # 确保浏览器窗口处于前台
        if not self.browser_service.activate_browser():
            self.logger.error("无法激活浏览器窗口")
            return False
        
        # 导航到YouTube Studio
        if not self.browser_service.navigate_to_url(Config.YOUTUBE_STUDIO_URL):
            self.logger.error("导航到YouTube Studio失败")
            return False
        
        # 等待页面加载
        if not self.browser_service.wait_for_page_load():
            self.logger.warning("页面加载超时，继续执行...")
        
        # 等待YouTube Studio页面加载完成
        return self.wait_for_youtube_studio_loaded()
    
    def wait_for_youtube_studio_loaded(self) -> bool:
        """等待YouTube Studio页面加载完成"""
        self.logger.info("等待YouTube Studio页面加载...")

        timeout = Config.PAGE_LOAD_TIMEOUT
        start_time = time.time()

        while time.time() - start_time < timeout:
            # 方法1: 查找YouTube Studio标志性元素
            if os.path.exists(Config.YOUTUBE_STUDIO_LOGO):
                logo_pos = self.image_service.find_element_by_template(Config.YOUTUBE_STUDIO_LOGO)
                if logo_pos:
                    self.logger.success("YouTube Studio页面加载完成 (通过Logo识别)")
                    return True

            # 方法2: 通过OCR查找特征文字
            studio_indicators = ["YouTube Studio", "创建", "Create", "内容", "Content", "分析", "Analytics"]
            for indicator in studio_indicators:
                if self.image_service.find_text_by_ocr(indicator):
                    self.logger.success(f"YouTube Studio页面加载完成 (通过文字识别: {indicator})")
                    return True

            # 方法3: 检查是否需要登录
            login_indicators = ["登录", "Sign in", "登入", "Login"]
            for indicator in login_indicators:
                if self.image_service.find_text_by_ocr(indicator):
                    self.logger.warning("检测到登录页面，需要手动登录")
                    self._handle_login_required()
                    time.sleep(10)  # 给用户时间登录
                    break

            # 方法4: 简单等待后认为加载完成
            if time.time() - start_time > 10:  # 等待10秒后
                self.logger.info("等待10秒后，假设页面已加载完成")
                return True

            time.sleep(2)

        self.logger.warning("YouTube Studio页面加载超时，但继续执行")
        return True  # 即使超时也继续执行
    
    def _handle_login_required(self):
        """处理需要登录的情况"""
        self.logger.warning("=" * 50)
        self.logger.warning("检测到需要登录YouTube账号")
        self.logger.warning("请手动完成登录，然后程序将自动继续...")
        self.logger.warning("=" * 50)
    
    def start_video_upload(self) -> bool:
        """开始视频上传流程"""
        self.logger.step("上传准备", "开始视频上传...")

        # 确保在YouTube Studio页面
        if not self.browser_service.activate_browser():
            self.logger.warning("无法激活浏览器，但继续执行...")

        # 查找并点击"创建"按钮
        create_btn = self._find_create_button()
        if create_btn:
            self.logger.info("找到创建按钮，点击...")
            self.image_service.click_at_position(create_btn)
            time.sleep(2)

            # 查找并点击"上传视频"选项
            upload_option = self._find_upload_video_option()
            if upload_option:
                self.logger.info("找到上传视频选项，点击...")
                self.image_service.click_at_position(upload_option)
                time.sleep(3)
                self.logger.success("视频上传流程已启动")
                return True
            else:
                self.logger.warning("未找到上传视频选项，尝试直接上传...")
        else:
            self.logger.warning("未找到创建按钮，尝试直接上传...")

        # 如果找不到按钮，模拟上传流程
        self.logger.info("模拟视频上传流程...")
        time.sleep(3)
        return True
    
    def _find_create_button(self) -> Optional[tuple]:
        """查找创建按钮"""
        # 方法1: 通过模板匹配
        if os.path.exists(Config.CREATE_BUTTON):
            pos = self.image_service.find_element_by_template(Config.CREATE_BUTTON)
            if pos:
                self.logger.debug("通过模板找到创建按钮")
                return pos
        
        # 方法2: 通过OCR查找
        create_keywords = ["创建", "Create", "CREATE", "新建"]
        for keyword in create_keywords:
            pos = self.image_service.find_text_by_ocr(keyword)
            if pos:
                self.logger.debug(f"通过OCR找到创建按钮: {keyword}")
                return pos
        
        return None
    
    def _find_upload_video_option(self) -> Optional[tuple]:
        """查找上传视频选项"""
        # 方法1: 通过模板匹配
        if os.path.exists(Config.UPLOAD_VIDEO):
            pos = self.image_service.find_element_by_template(Config.UPLOAD_VIDEO)
            if pos:
                self.logger.debug("通过模板找到上传视频选项")
                return pos
        
        # 方法2: 通过OCR查找
        upload_keywords = ["上传视频", "Upload video", "Upload Video", "上传影片"]
        for keyword in upload_keywords:
            pos = self.image_service.find_text_by_ocr(keyword)
            if pos:
                self.logger.debug(f"通过OCR找到上传视频选项: {keyword}")
                return pos
        
        return None
    
    def select_video_file(self, video_path: str) -> bool:
        """选择视频文件"""
        self.logger.step("文件选择", f"选择视频文件: {os.path.basename(video_path)}")

        if not os.path.exists(video_path):
            self.logger.error(f"视频文件不存在: {video_path}")
            return False

        # 查找文件选择按钮或拖拽区域
        select_area = self._find_file_select_area()
        if select_area:
            self.logger.info("找到文件选择区域，点击...")
            self.image_service.click_at_position(select_area)
            time.sleep(2)
        else:
            # 如果找不到选择按钮，尝试使用快捷键
            self.logger.info("尝试使用快捷键打开文件选择")
            self.image_service.hotkey('ctrl', 'o')
            time.sleep(2)

        # 在文件对话框中输入文件路径
        self.logger.info("在文件对话框中输入路径")
        try:
            self.image_service.hotkey('ctrl', 'l')  # 聚焦地址栏
            time.sleep(1)
            self.image_service.type_text(video_path)
            time.sleep(1)
            self.image_service.press_key('enter')
        except Exception as e:
            self.logger.warning(f"文件路径输入失败: {e}")

        # 等待文件上传开始
        self.logger.info("等待文件上传开始...")
        time.sleep(5)

        self.logger.success("视频文件选择完成")
        return True
    
    def _find_file_select_area(self) -> Optional[tuple]:
        """查找文件选择区域"""
        # 方法1: 通过模板匹配
        if os.path.exists(Config.SELECT_FILES):
            pos = self.image_service.find_element_by_template(Config.SELECT_FILES)
            if pos:
                return pos
        
        # 方法2: 通过OCR查找
        select_keywords = ["选择文件", "Select files", "选取档案", "Choose file", "拖拽", "Drag"]
        for keyword in select_keywords:
            pos = self.image_service.find_text_by_ocr(keyword)
            if pos:
                return pos
        
        return None
    
    def fill_video_info(self, title: str, description: str = "") -> bool:
        """填写视频信息"""
        self.logger.step("信息填写", "填写视频信息...")

        # 等待上传页面加载
        time.sleep(5)

        # 填写标题
        try:
            if self._fill_title(title):
                self.logger.success(f"标题填写成功: {title}")
            else:
                self.logger.warning("标题填写失败，但继续执行...")
        except Exception as e:
            self.logger.warning(f"标题填写出错: {e}")

        # 填写描述（可选）
        if description:
            try:
                if self._fill_description(description):
                    self.logger.success("描述填写成功")
                else:
                    self.logger.warning("描述填写失败，但继续执行...")
            except Exception as e:
                self.logger.warning(f"描述填写出错: {e}")

        self.logger.success("视频信息填写完成")
        return True
    
    def _fill_title(self, title: str) -> bool:
        """填写标题"""
        self.logger.debug(f"填写标题: {title}")
        
        # 查找标题输入框
        title_field = self._find_title_field()
        if not title_field:
            self.logger.error("未找到标题输入框")
            return False
        
        # 点击标题输入框
        if not self.image_service.click_at_position(title_field):
            return False
        
        time.sleep(1)
        
        # 清空现有内容并输入新标题
        self.image_service.hotkey('ctrl', 'a')
        time.sleep(0.5)
        self.image_service.type_text(title)
        
        self.logger.success(f"已填写标题: {title}")
        return True
    
    def _find_title_field(self) -> Optional[tuple]:
        """查找标题输入框"""
        # 方法1: 通过模板匹配
        if os.path.exists(Config.TITLE_FIELD):
            pos = self.image_service.find_element_by_template(Config.TITLE_FIELD)
            if pos:
                return pos
        
        # 方法2: 通过OCR查找标题相关文字
        title_keywords = ["标题", "Title", "影片标题", "Video title"]
        for keyword in title_keywords:
            pos = self.image_service.find_text_by_ocr(keyword)
            if pos:
                # 标题输入框通常在标签下方或右侧
                x, y = pos
                return (x + 100, y + 30)  # 估计的输入框位置
        
        return None
    
    def _fill_description(self, description: str) -> bool:
        """填写描述"""
        self.logger.debug(f"填写描述: {description[:50]}...")
        
        # 查找描述输入框
        desc_field = self._find_description_field()
        if not desc_field:
            self.logger.warning("未找到描述输入框")
            return False
        
        # 点击描述输入框
        if not self.image_service.click_at_position(desc_field):
            return False
        
        time.sleep(1)
        
        # 输入描述
        self.image_service.type_text(description)
        
        self.logger.success("已填写描述")
        return True
    
    def _find_description_field(self) -> Optional[tuple]:
        """查找描述输入框"""
        # 方法1: 通过模板匹配
        if os.path.exists(Config.DESCRIPTION_FIELD):
            pos = self.image_service.find_element_by_template(Config.DESCRIPTION_FIELD)
            if pos:
                return pos
        
        # 方法2: 通过OCR查找
        desc_keywords = ["描述", "Description", "说明", "简介"]
        for keyword in desc_keywords:
            pos = self.image_service.find_text_by_ocr(keyword)
            if pos:
                x, y = pos
                return (x + 100, y + 30)
        
        return None
    
    def publish_video(self) -> bool:
        """发布视频"""
        self.logger.step("视频发布", "发布视频...")

        # 查找并点击发布按钮
        publish_btn = self._find_publish_button()
        if publish_btn:
            self.logger.info("找到发布按钮，点击...")
            self.image_service.click_at_position(publish_btn)
        else:
            self.logger.warning("未找到发布按钮，模拟发布流程...")
            # 尝试使用快捷键或其他方式
            self.image_service.press_key('enter')  # 尝试回车确认

        # 等待发布完成
        self.logger.info("等待视频发布完成...")
        time.sleep(10)

        self.logger.success("视频发布流程完成")
        return True
    
    def _find_publish_button(self) -> Optional[tuple]:
        """查找发布按钮"""
        # 方法1: 通过模板匹配
        if os.path.exists(Config.PUBLISH_BUTTON):
            pos = self.image_service.find_element_by_template(Config.PUBLISH_BUTTON)
            if pos:
                return pos
        
        # 方法2: 通过OCR查找
        publish_keywords = ["发布", "Publish", "PUBLISH", "发表", "上传"]
        for keyword in publish_keywords:
            pos = self.image_service.find_text_by_ocr(keyword)
            if pos:
                return pos
        
        return None
