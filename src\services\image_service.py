# services/image_service.py
import cv2
import numpy as np
import pyautogui
import time
import os
from typing import Optional, Tuple, List
try:
    from ..config.settings import Config
    from ..utils.logger import get_logger
except ImportError:
    from config.settings import Config
    from utils.logger import get_logger

class ImageService:
    """图像识别服务"""
    
    def __init__(self):
        self.logger = get_logger("ImageService")
        self.template_threshold = Config.TEMPLATE_THRESHOLD
        
        # 禁用pyautogui的安全检查
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0.5
    
    def find_element_by_template(self, template_path: str, confidence: float = None) -> Optional[Tuple[int, int]]:
        """通过模板匹配查找元素"""
        try:
            if not os.path.exists(template_path):
                self.logger.error(f"模板文件不存在: {template_path}")
                return None
            
            # 截取屏幕
            screenshot = pyautogui.screenshot()
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 加载模板
            template = cv2.imread(template_path)
            if template is None:
                self.logger.error(f"无法加载模板: {template_path}")
                return None
            
            # 模板匹配
            result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            threshold = confidence if confidence else self.template_threshold
            
            if max_val >= threshold:
                # 返回中心点坐标
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                self.logger.debug(f"找到模板 {os.path.basename(template_path)} at ({center_x}, {center_y}), 置信度: {max_val:.3f}")
                return (center_x, center_y)
            else:
                self.logger.debug(f"模板匹配失败 {os.path.basename(template_path)}, 置信度: {max_val:.3f} < {threshold}")
                return None
                
        except Exception as e:
            self.logger.error(f"模板匹配出错: {e}")
            return None
    
    def find_element_in_area(self, template_path: str, area: Tuple[int, int, int, int]) -> Optional[Tuple[int, int]]:
        """在指定区域内查找元素"""
        try:
            x, y, width, height = area
            
            # 截取指定区域
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 加载模板
            template = cv2.imread(template_path)
            if template is None:
                return None
            
            # 模板匹配
            result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= self.template_threshold:
                # 转换为全屏坐标
                h, w = template.shape[:2]
                center_x = x + max_loc[0] + w // 2
                center_y = y + max_loc[1] + h // 2
                return (center_x, center_y)
            
            return None
            
        except Exception as e:
            self.logger.error(f"区域模板匹配出错: {e}")
            return None
    
    def find_text_by_ocr(self, text: str, area: Optional[Tuple[int, int, int, int]] = None) -> Optional[Tuple[int, int]]:
        """通过OCR查找文字"""
        try:
            import pytesseract
            # 配置Tesseract路径
            pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
            
            if area:
                x, y, width, height = area
                screenshot = pyautogui.screenshot(region=(x, y, width, height))
                offset_x, offset_y = x, y
            else:
                screenshot = pyautogui.screenshot()
                offset_x, offset_y = 0, 0
            
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # OCR识别
            data = pytesseract.image_to_data(screenshot_cv, output_type=pytesseract.Output.DICT, lang='chi_sim+eng')
            
            for i, detected_text in enumerate(data['text']):
                if text.lower() in detected_text.lower() and int(data['conf'][i]) > Config.OCR_CONFIDENCE_THRESHOLD:
                    x = offset_x + data['left'][i] + data['width'][i] // 2
                    y = offset_y + data['top'][i] + data['height'][i] // 2
                    self.logger.debug(f"找到文字 '{text}' at ({x}, {y})")
                    return (x, y)
            
            return None
            
        except ImportError:
            self.logger.error("pytesseract未安装，无法使用OCR功能")
            return None
        except Exception as e:
            self.logger.error(f"OCR识别出错: {e}")
            return None
    
    def click_at_position(self, position: Tuple[int, int], button: str = 'left') -> bool:
        """在指定位置点击"""
        try:
            x, y = position
            self.logger.debug(f"点击位置: ({x}, {y})")
            
            pyautogui.click(x, y, button=button)
            time.sleep(Config.CLICK_INTERVAL)
            return True
            
        except Exception as e:
            self.logger.error(f"点击操作失败: {e}")
            return False
    
    def double_click_at_position(self, position: Tuple[int, int]) -> bool:
        """在指定位置双击"""
        try:
            x, y = position
            self.logger.debug(f"双击位置: ({x}, {y})")
            
            pyautogui.doubleClick(x, y)
            time.sleep(Config.CLICK_INTERVAL)
            return True
            
        except Exception as e:
            self.logger.error(f"双击操作失败: {e}")
            return False
    
    def type_text(self, text: str, interval: float = 0.1) -> bool:
        """输入文字"""
        try:
            self.logger.debug(f"输入文字: {text}")
            pyautogui.typewrite(text, interval=interval)
            return True
            
        except Exception as e:
            self.logger.error(f"文字输入失败: {e}")
            return False
    
    def press_key(self, key: str) -> bool:
        """按键"""
        try:
            self.logger.debug(f"按键: {key}")
            pyautogui.press(key)
            return True
            
        except Exception as e:
            self.logger.error(f"按键失败: {e}")
            return False
    
    def hotkey(self, *keys) -> bool:
        """组合键"""
        try:
            self.logger.debug(f"组合键: {'+'.join(keys)}")
            pyautogui.hotkey(*keys)
            return True
            
        except Exception as e:
            self.logger.error(f"组合键失败: {e}")
            return False
    
    def wait_for_element(self, template_path: str, timeout: int = 30) -> Optional[Tuple[int, int]]:
        """等待元素出现"""
        self.logger.info(f"等待元素出现: {os.path.basename(template_path)}")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            position = self.find_element_by_template(template_path)
            if position:
                self.logger.success(f"元素已出现: {os.path.basename(template_path)}")
                return position
            time.sleep(1)
        
        self.logger.warning(f"等待元素超时: {os.path.basename(template_path)}")
        return None
