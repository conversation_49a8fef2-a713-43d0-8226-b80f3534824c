@echo off
echo Hub Studio 全自动化视频上传系统
echo ================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查是否提供了视频目录参数
if "%1"=="" (
    echo 使用方法: start.bat ^<视频目录路径^>
    echo.
    echo 示例:
    echo   start.bat C:\Videos
    echo   start.bat videos
    echo.
    pause
    exit /b 1
)

REM 检查视频目录是否存在
if not exist "%1" (
    echo 错误: 视频目录不存在: %1
    pause
    exit /b 1
)

echo 正在启动自动化系统...
echo 视频目录: %1
echo.

REM 运行主程序
python run.py "%1"

echo.
echo 程序执行完成
pause
