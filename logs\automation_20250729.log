2025-07-29 13:22:29,529 - WindowService - ERROR - 获取活动窗口失败: module 'win32gui' has no attribute 'GetWindowThreadProcessId'
2025-07-29 13:22:30,195 - TestLogger - INFO - 这是一条信息日志
2025-07-29 13:22:30,195 - TestLogger - WARNING - 这是一条警告日志
2025-07-29 13:22:30,195 - TestLogger - INFO - SUCCESS: 这是一条成功日志
2025-07-29 13:22:30,195 - TestLogger - INFO - [测试步骤] 这是一条步骤日志
2025-07-29 13:22:30,196 - ImageService - ERROR - 模板文件不存在: non_existent_template.png
2025-07-29 13:22:30,197 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:23:25,656 - SimulationTest - INFO - 模拟测试日志
2025-07-29 13:31:27,490 - AutomationController - INFO - ============================================================
2025-07-29 13:31:27,490 - AutomationController - INFO - 🚀 Hub Studio 全自动化视频上传系统
2025-07-29 13:31:27,490 - AutomationController - INFO - ============================================================
2025-07-29 13:31:27,490 - AutomationController - INFO - [系统检查] 验证系统设置...
2025-07-29 13:31:27,490 - AutomationController - INFO - SUCCESS: 系统设置验证通过
2025-07-29 13:31:27,491 - AutomationController - INFO - [启动应用] 启动Hub Studio...
2025-07-29 13:31:27,996 - HubStudioService - INFO - 正在启动Hub Studio...
2025-07-29 13:31:31,048 - HubStudioService - INFO - 等待Hub Studio进程启动 (超时: 60秒)...
2025-07-29 13:31:31,054 - HubStudioService - INFO - SUCCESS: Hub Studio进程启动成功
2025-07-29 13:31:31,054 - HubStudioService - INFO - 等待Hub Studio界面准备就绪 (超时: 60秒)...
2025-07-29 13:31:31,055 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:31,056 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:31,057 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:31,057 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:33,059 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:33,059 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:33,061 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:33,061 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:35,063 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:35,064 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:35,066 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:35,068 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:37,070 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:37,072 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:37,074 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:37,076 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:39,079 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:39,080 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:39,080 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:39,081 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:41,082 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:41,083 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:41,084 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:41,085 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:43,088 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:43,090 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:43,091 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:43,093 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:45,095 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:45,097 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:45,099 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:45,100 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:47,103 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:47,105 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:47,107 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:47,109 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:49,112 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:49,114 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:49,115 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:49,116 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:51,118 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:51,119 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:51,121 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:51,122 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:53,126 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:53,128 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:53,129 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:53,131 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:55,133 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:55,135 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:55,136 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:55,138 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:57,142 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:57,144 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:57,145 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:57,147 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:59,149 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:59,151 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:59,152 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:31:59,154 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:01,156 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:01,158 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:01,160 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:01,161 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:03,164 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:03,165 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:03,167 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:03,169 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:05,171 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:05,175 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:05,176 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:05,178 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:07,180 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:07,182 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:07,183 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:07,185 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:09,189 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:09,190 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:09,192 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:09,193 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:11,195 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:11,196 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:11,198 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:11,199 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:13,202 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:13,204 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:13,207 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:13,209 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:15,211 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:15,213 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:15,215 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:15,216 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:16,781 - AutomationController - INFO - ============================================================
2025-07-29 13:32:16,781 - AutomationController - INFO - 🚀 Hub Studio 全自动化视频上传系统
2025-07-29 13:32:16,782 - AutomationController - INFO - ============================================================
2025-07-29 13:32:16,782 - AutomationController - INFO - [系统检查] 验证系统设置...
2025-07-29 13:32:16,782 - AutomationController - INFO - SUCCESS: 系统设置验证通过
2025-07-29 13:32:16,782 - AutomationController - INFO - [启动应用] 启动Hub Studio...
2025-07-29 13:32:16,832 - HubStudioService - INFO - Hub Studio已在运行
2025-07-29 13:32:16,833 - HubStudioService - INFO - 等待Hub Studio界面准备就绪 (超时: 60秒)...
2025-07-29 13:32:16,834 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:16,834 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:16,835 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:16,835 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:17,218 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:17,219 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:17,220 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:17,221 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:18,839 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:18,841 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:18,842 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:18,844 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:19,222 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:19,223 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:19,225 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:19,226 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:20,847 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:20,848 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:20,850 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:20,852 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:21,227 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:21,227 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:21,228 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:21,229 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:22,855 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:22,857 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:22,858 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:22,860 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:23,230 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:23,231 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:23,232 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:23,233 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:24,862 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:24,863 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:24,865 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:24,866 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:25,234 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:25,235 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:25,236 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:25,237 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:26,868 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:26,871 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:26,872 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:26,874 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:27,238 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:27,239 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:27,240 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:27,240 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:28,877 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:28,878 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:28,880 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:28,881 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:29,242 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:29,242 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:29,243 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:29,245 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:30,883 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:30,885 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:30,887 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:30,888 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:31,246 - HubStudioService - WARNING - 等待Hub Studio界面超时
2025-07-29 13:32:31,246 - AutomationController - ERROR - Hub Studio启动失败
2025-07-29 13:32:32,890 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:32,891 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:32,893 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:32,894 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:34,895 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:34,896 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:34,897 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:34,898 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:36,899 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:36,901 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:36,902 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:36,902 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:38,904 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:38,905 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:38,905 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:38,906 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:40,907 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:40,908 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:40,909 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:40,909 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:42,910 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:42,912 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:42,913 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:42,913 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:44,915 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:44,917 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:44,918 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:44,919 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:46,920 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:46,921 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:46,921 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:46,921 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:48,923 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:48,924 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:48,924 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:48,925 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:50,927 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:50,928 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:50,928 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:50,929 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:52,930 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:52,931 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:52,933 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:52,934 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:54,936 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:54,937 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:54,937 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:54,938 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:56,939 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:56,940 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:56,941 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:56,941 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:58,942 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:58,943 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:58,944 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:32:58,944 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:00,945 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:00,946 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:00,948 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:00,949 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:02,950 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:02,951 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:02,952 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:02,952 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:04,953 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:04,954 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:04,954 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:04,955 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:06,956 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:06,957 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:06,958 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:06,959 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:08,960 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:08,961 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:08,962 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:08,964 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:10,965 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:10,966 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:10,967 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:10,968 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:12,969 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:12,970 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:12,971 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:12,972 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:14,974 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:14,975 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:14,976 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:14,977 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:33:16,978 - HubStudioService - WARNING - 等待Hub Studio界面超时
2025-07-29 13:33:16,979 - AutomationController - ERROR - Hub Studio启动失败
2025-07-29 13:36:51,919 - AutomationController - INFO - ============================================================
2025-07-29 13:36:51,920 - AutomationController - INFO - 🚀 Hub Studio 全自动化视频上传系统
2025-07-29 13:36:51,920 - AutomationController - INFO - ============================================================
2025-07-29 13:36:51,920 - AutomationController - INFO - [系统检查] 验证系统设置...
2025-07-29 13:36:51,921 - AutomationController - INFO - SUCCESS: 系统设置验证通过
2025-07-29 13:36:51,921 - AutomationController - INFO - [启动应用] 启动Hub Studio...
2025-07-29 13:36:52,132 - HubStudioService - INFO - Hub Studio已在运行
2025-07-29 13:36:52,132 - HubStudioService - INFO - 等待Hub Studio界面准备就绪 (超时: 60秒)...
2025-07-29 13:36:52,133 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:52,134 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:52,135 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:52,136 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:54,137 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:54,139 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:54,139 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:54,140 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:56,142 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:56,143 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:56,143 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:56,144 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:58,146 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:58,147 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:58,148 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:36:58,148 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:37:00,149 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:37:00,150 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:37:00,151 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:37:00,151 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:37:02,152 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:37:02,154 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:37:02,154 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:37:02,155 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:37:02,160 - HubStudioService - INFO - Hub Studio进程运行中，假设界面已准备就绪
2025-07-29 13:37:02,161 - AutomationController - INFO - SUCCESS: ✓ Hub Studio启动成功
2025-07-29 13:37:02,161 - AutomationController - INFO - [环境分析] 分析Hub Studio环境列表...
2025-07-29 13:37:02,161 - EnvironmentService - INFO - [环境分析] 开始分析环境列表...
2025-07-29 13:37:02,162 - WindowService - INFO - 查找Hub Studio窗口...
2025-07-29 13:37:02,163 - WindowService - WARNING - 未找到Hub Studio窗口
2025-07-29 13:37:02,164 - EnvironmentService - ERROR - 无法激活Hub Studio窗口
2025-07-29 13:37:02,164 - AutomationController - ERROR - 未发现任何可用环境
2025-07-29 13:38:17,055 - AutomationController - INFO - ============================================================
2025-07-29 13:38:17,056 - AutomationController - INFO - 🚀 Hub Studio 全自动化视频上传系统
2025-07-29 13:38:17,056 - AutomationController - INFO - ============================================================
2025-07-29 13:38:17,056 - AutomationController - INFO - [系统检查] 验证系统设置...
2025-07-29 13:38:17,057 - AutomationController - INFO - SUCCESS: 系统设置验证通过
2025-07-29 13:38:17,057 - AutomationController - INFO - [启动应用] 启动Hub Studio...
2025-07-29 13:38:17,262 - HubStudioService - INFO - Hub Studio已在运行
2025-07-29 13:38:17,262 - HubStudioService - INFO - 等待Hub Studio界面准备就绪 (超时: 60秒)...
2025-07-29 13:38:17,263 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:17,264 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:17,265 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:17,266 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:19,267 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:19,268 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:19,269 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:19,270 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:21,271 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:21,272 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:21,273 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:21,273 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:23,274 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:23,275 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:23,275 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:23,276 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:25,277 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:25,278 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:25,278 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:25,279 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:27,280 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:27,281 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:27,282 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:27,283 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:27,291 - HubStudioService - INFO - Hub Studio进程运行中，假设界面已准备就绪
2025-07-29 13:38:27,291 - AutomationController - INFO - SUCCESS: ✓ Hub Studio启动成功
2025-07-29 13:38:27,292 - AutomationController - INFO - [环境分析] 分析Hub Studio环境列表...
2025-07-29 13:38:27,292 - EnvironmentService - INFO - [环境分析] 开始分析环境列表...
2025-07-29 13:38:27,293 - WindowService - INFO - 查找Hub Studio窗口...
2025-07-29 13:38:27,294 - WindowService - WARNING - 未找到Hub Studio窗口，但继续执行
2025-07-29 13:38:27,294 - EnvironmentService - WARNING - 无法激活Hub Studio窗口，但继续分析环境...
2025-07-29 13:38:29,296 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,297 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,298 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,299 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,301 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,302 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,303 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,304 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,306 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,307 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,308 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,309 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,309 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,310 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,311 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,312 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,313 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,314 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,314 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,315 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,316 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,318 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,318 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,320 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:38:29,326 - EnvironmentService - WARNING - 无法识别环境项目，创建默认环境用于测试
2025-07-29 13:38:29,326 - EnvironmentService - INFO - SUCCESS: 发现 3 个环境
2025-07-29 13:38:29,327 - EnvironmentService - INFO -   环境 1: 默认环境_1
2025-07-29 13:38:29,327 - EnvironmentService - INFO -   环境 2: 默认环境_2
2025-07-29 13:38:29,327 - EnvironmentService - INFO -   环境 3: 默认环境_3
2025-07-29 13:38:29,328 - AutomationController - INFO - SUCCESS: ✓ 发现 3 个可用环境
2025-07-29 13:38:29,328 - AutomationController - INFO -   环境 1: 默认环境_1 (default)
2025-07-29 13:38:29,328 - AutomationController - INFO -   环境 2: 默认环境_2 (default)
2025-07-29 13:38:29,329 - AutomationController - INFO -   环境 3: 默认环境_3 (default)
2025-07-29 13:38:29,329 - AutomationController - INFO - [文件扫描] 扫描视频目录: videos
2025-07-29 13:38:29,329 - AutomationController - INFO -   ✓ test_video.mp4 (0.0MB)
2025-07-29 13:38:29,330 - AutomationController - INFO - SUCCESS: 找到 1 个有效视频文件
2025-07-29 13:38:29,330 - AutomationController - INFO - [批量上传] 开始批量上传 1 个视频...
2025-07-29 13:38:29,330 - AutomationController - INFO - ==================================================
2025-07-29 13:38:29,330 - AutomationController - INFO - 📹 上传视频 1/1
2025-07-29 13:38:29,331 - AutomationController - INFO - 视频: test_video.mp4
2025-07-29 13:38:29,331 - AutomationController - INFO - 环境: 默认环境_1
2025-07-29 13:38:29,331 - AutomationController - INFO - ==================================================
2025-07-29 13:38:29,331 - AutomationController - INFO - [环境启动] 打开环境: 默认环境_1
2025-07-29 13:38:29,331 - EnvironmentService - INFO - [打开环境] 正在打开环境: 默认环境_1
2025-07-29 13:38:29,332 - WindowService - INFO - 查找Hub Studio窗口...
2025-07-29 13:38:29,332 - WindowService - WARNING - 未找到Hub Studio窗口，但继续执行
2025-07-29 13:38:29,333 - EnvironmentService - WARNING - 无法激活Hub Studio窗口，继续执行...
2025-07-29 13:38:29,333 - EnvironmentService - INFO - 点击环境位置: (225, 200)
2025-07-29 13:38:31,854 - EnvironmentService - INFO - 等待浏览器窗口启动...
2025-07-29 13:38:31,856 - WindowService - INFO - 等待新浏览器窗口出现...
2025-07-29 13:38:46,871 - WindowService - WARNING - 等待新浏览器窗口超时
2025-07-29 13:38:46,872 - EnvironmentService - WARNING - 未检测到新浏览器窗口，但继续执行流程
2025-07-29 13:38:46,873 - AutomationController - INFO - [页面导航] 跳转到YouTube Studio
2025-07-29 13:38:46,873 - YouTubeService - INFO - [YouTube导航] 正在跳转到YouTube Studio...
2025-07-29 13:38:46,874 - BrowserService - ERROR - 未找到浏览器窗口
2025-07-29 13:38:46,875 - YouTubeService - ERROR - 无法激活浏览器窗口
2025-07-29 13:38:46,875 - AutomationController - ERROR - ✗ 视频 1 上传失败
2025-07-29 13:38:46,876 - AutomationController - INFO - ============================================================
2025-07-29 13:38:46,876 - AutomationController - INFO - 📊 批量上传完成
2025-07-29 13:38:46,877 - AutomationController - INFO - 成功: 0
2025-07-29 13:38:46,877 - AutomationController - INFO - 失败: 1
2025-07-29 13:38:46,878 - AutomationController - INFO - 总计: 1
2025-07-29 13:38:46,879 - AutomationController - INFO - 成功率: 0.0%
2025-07-29 13:38:46,880 - AutomationController - INFO - ============================================================
2025-07-29 13:39:48,833 - WindowService - ERROR - 获取活动窗口失败: module 'win32gui' has no attribute 'GetWindowThreadProcessId'
2025-07-29 13:39:50,903 - EnvironmentService - INFO - [环境分析] 开始分析环境列表...
2025-07-29 13:39:50,903 - WindowService - INFO - 查找Hub Studio窗口...
2025-07-29 13:39:50,904 - WindowService - WARNING - 未找到Hub Studio窗口，但继续执行
2025-07-29 13:39:50,905 - EnvironmentService - WARNING - 无法激活Hub Studio窗口，但继续分析环境...
2025-07-29 13:39:52,907 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,908 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,908 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,909 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,910 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,910 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,911 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,911 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,912 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,913 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,914 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,915 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,916 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,917 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,918 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,918 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,919 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,920 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,920 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,922 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,923 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,924 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,924 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,925 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:39:52,928 - EnvironmentService - WARNING - 无法识别环境项目，创建默认环境用于测试
2025-07-29 13:39:52,929 - EnvironmentService - INFO - SUCCESS: 发现 3 个环境
2025-07-29 13:39:52,930 - EnvironmentService - INFO -   环境 1: 默认环境_1
2025-07-29 13:39:52,930 - EnvironmentService - INFO -   环境 2: 默认环境_2
2025-07-29 13:39:52,930 - EnvironmentService - INFO -   环境 3: 默认环境_3
2025-07-29 13:43:52,677 - HubStudioService - INFO - Hub Studio已在运行
2025-07-29 13:43:52,678 - HubStudioService - INFO - 等待Hub Studio界面准备就绪 (超时: 60秒)...
2025-07-29 13:43:52,678 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:52,678 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:52,679 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:52,680 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:54,681 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:54,682 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:54,683 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:54,683 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:56,685 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:56,687 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:56,688 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:56,689 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:58,690 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:58,691 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:58,692 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:43:58,693 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:44:00,694 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:44:00,695 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:44:00,695 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:44:00,696 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:44:02,697 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:44:02,699 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:44:02,700 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:44:02,701 - ImageService - ERROR - pytesseract未安装，无法使用OCR功能
2025-07-29 13:44:02,707 - HubStudioService - INFO - Hub Studio进程运行中，假设界面已准备就绪
2025-07-29 14:03:32,006 - AutomationController - INFO - ============================================================
2025-07-29 14:03:32,007 - AutomationController - INFO - 🚀 Hub Studio 全自动化视频上传系统
2025-07-29 14:03:32,007 - AutomationController - INFO - ============================================================
2025-07-29 14:03:32,007 - AutomationController - INFO - [系统检查] 验证系统设置...
2025-07-29 14:03:32,007 - AutomationController - INFO - SUCCESS: 系统设置验证通过
2025-07-29 14:03:32,007 - AutomationController - INFO - [启动应用] 启动Hub Studio...
2025-07-29 14:03:32,655 - HubStudioService - INFO - 正在启动Hub Studio...
2025-07-29 14:03:35,685 - HubStudioService - INFO - 等待Hub Studio进程启动 (超时: 60秒)...
2025-07-29 14:03:35,691 - HubStudioService - INFO - SUCCESS: Hub Studio进程启动成功
2025-07-29 14:03:35,691 - HubStudioService - INFO - 等待Hub Studio界面准备就绪 (超时: 60秒)...
2025-07-29 14:03:53,197 - HubStudioService - INFO - Hub Studio进程运行中，假设界面已准备就绪
2025-07-29 14:03:53,198 - AutomationController - INFO - SUCCESS: ✓ Hub Studio启动成功
2025-07-29 14:03:53,198 - AutomationController - INFO - [环境分析] 分析Hub Studio环境列表...
2025-07-29 14:03:53,199 - EnvironmentService - INFO - [环境分析] 开始分析环境列表...
2025-07-29 14:03:53,199 - WindowService - INFO - 查找Hub Studio窗口...
2025-07-29 14:03:53,200 - WindowService - WARNING - 未找到Hub Studio窗口，但继续执行
2025-07-29 14:03:53,200 - EnvironmentService - WARNING - 无法激活Hub Studio窗口，但继续分析环境...
2025-07-29 14:04:07,979 - EnvironmentService - INFO - SUCCESS: 发现 7 个环境
2025-07-29 14:04:07,979 - EnvironmentService - INFO -   环境 1: a
2025-07-29 14:04:07,979 - EnvironmentService - INFO -   环境 2: Be
2025-07-29 14:04:07,980 - EnvironmentService - INFO -   环境 3: fullscreen 20250729 135549pnq
2025-07-29 14:04:07,980 - EnvironmentService - INFO -   环境 4: mentst
2025-07-29 14:04:07,981 - EnvironmentService - INFO -   环境 5: ort 20250729 132447md
2025-07-29 14:04:07,981 - EnvironmentService - INFO -   环境 6: ee
2025-07-29 14:04:07,981 - EnvironmentService - INFO -   环境 7: ee
2025-07-29 14:04:07,981 - AutomationController - INFO - SUCCESS: ✓ 发现 7 个可用环境
2025-07-29 14:04:07,982 - AutomationController - INFO -   环境 1: a (scanning)
2025-07-29 14:04:07,982 - AutomationController - INFO -   环境 2: Be (scanning)
2025-07-29 14:04:07,982 - AutomationController - INFO -   环境 3: fullscreen 20250729 135549pnq (scanning)
2025-07-29 14:04:07,982 - AutomationController - INFO -   环境 4: mentst (scanning)
2025-07-29 14:04:07,982 - AutomationController - INFO -   环境 5: ort 20250729 132447md (scanning)
2025-07-29 14:04:07,982 - AutomationController - INFO -   环境 6: ee (scanning)
2025-07-29 14:04:07,983 - AutomationController - INFO -   环境 7: ee (scanning)
2025-07-29 14:04:07,983 - AutomationController - INFO - [文件扫描] 扫描视频目录: videos
2025-07-29 14:04:07,983 - AutomationController - INFO -   ✓ test_video.mp4 (0.0MB)
2025-07-29 14:04:07,983 - AutomationController - INFO - SUCCESS: 找到 1 个有效视频文件
2025-07-29 14:04:07,983 - AutomationController - INFO - [批量上传] 开始批量上传 1 个视频...
2025-07-29 14:04:07,984 - AutomationController - INFO - ==================================================
2025-07-29 14:04:07,984 - AutomationController - INFO - 📹 上传视频 1/1
2025-07-29 14:04:07,984 - AutomationController - INFO - 视频: test_video.mp4
2025-07-29 14:04:07,984 - AutomationController - INFO - 环境: a
2025-07-29 14:04:07,984 - AutomationController - INFO - ==================================================
2025-07-29 14:04:07,985 - AutomationController - INFO - [环境启动] 打开环境: a
2025-07-29 14:04:07,985 - EnvironmentService - INFO - [打开环境] 正在打开环境: a
2025-07-29 14:04:07,985 - WindowService - INFO - 查找Hub Studio窗口...
2025-07-29 14:04:07,985 - WindowService - WARNING - 未找到Hub Studio窗口，但继续执行
2025-07-29 14:04:07,986 - EnvironmentService - WARNING - 无法激活Hub Studio窗口，继续执行...
2025-07-29 14:04:07,986 - EnvironmentService - INFO - 点击环境位置: (225, 200)
2025-07-29 14:04:10,495 - EnvironmentService - INFO - 等待浏览器窗口启动...
2025-07-29 14:04:10,498 - WindowService - INFO - 等待新浏览器窗口出现...
2025-07-29 14:04:25,517 - WindowService - WARNING - 等待新浏览器窗口超时
2025-07-29 14:04:25,518 - EnvironmentService - WARNING - 未检测到新浏览器窗口，但继续执行流程
2025-07-29 14:04:25,519 - AutomationController - INFO - [页面导航] 跳转到YouTube Studio
2025-07-29 14:04:25,519 - YouTubeService - INFO - [YouTube导航] 正在跳转到YouTube Studio...
2025-07-29 14:04:25,521 - BrowserService - ERROR - 未找到浏览器窗口
2025-07-29 14:04:25,521 - YouTubeService - ERROR - 无法激活浏览器窗口
2025-07-29 14:04:25,522 - AutomationController - ERROR - ✗ 视频 1 上传失败
2025-07-29 14:04:25,522 - AutomationController - INFO - ============================================================
2025-07-29 14:04:25,523 - AutomationController - INFO - 📊 批量上传完成
2025-07-29 14:04:25,523 - AutomationController - INFO - 成功: 0
2025-07-29 14:04:25,524 - AutomationController - INFO - 失败: 1
2025-07-29 14:04:25,525 - AutomationController - INFO - 总计: 1
2025-07-29 14:04:25,526 - AutomationController - INFO - 成功率: 0.0%
2025-07-29 14:04:25,526 - AutomationController - INFO - ============================================================
2025-07-29 14:05:08,062 - AutomationController - INFO - ============================================================
2025-07-29 14:05:08,062 - AutomationController - INFO - 🚀 Hub Studio 全自动化视频上传系统
2025-07-29 14:05:08,062 - AutomationController - INFO - ============================================================
2025-07-29 14:05:08,063 - AutomationController - INFO - [系统检查] 验证系统设置...
2025-07-29 14:05:08,063 - AutomationController - INFO - SUCCESS: 系统设置验证通过
2025-07-29 14:05:08,063 - AutomationController - INFO - [启动应用] 启动Hub Studio...
2025-07-29 14:05:08,570 - HubStudioService - INFO - Hub Studio已在运行
2025-07-29 14:05:08,570 - HubStudioService - INFO - 等待Hub Studio界面准备就绪 (超时: 60秒)...
2025-07-29 14:05:28,158 - HubStudioService - INFO - Hub Studio进程运行中，假设界面已准备就绪
2025-07-29 14:05:28,158 - AutomationController - INFO - SUCCESS: ✓ Hub Studio启动成功
2025-07-29 14:05:28,159 - AutomationController - INFO - [环境分析] 分析Hub Studio环境列表...
2025-07-29 14:05:28,159 - EnvironmentService - INFO - [环境分析] 开始分析环境列表...
2025-07-29 14:05:28,160 - WindowService - INFO - 查找Hub Studio窗口...
2025-07-29 14:05:28,161 - WindowService - WARNING - 未找到Hub Studio窗口，但继续执行
2025-07-29 14:05:28,162 - EnvironmentService - WARNING - 无法激活Hub Studio窗口，但继续分析环境...
2025-07-29 14:05:45,898 - EnvironmentService - INFO - SUCCESS: 发现 7 个环境
2025-07-29 14:05:45,898 - EnvironmentService - INFO -   环境 1: a
2025-07-29 14:05:45,899 - EnvironmentService - INFO -   环境 2: Be
2025-07-29 14:05:45,899 - EnvironmentService - INFO -   环境 3: fullscreen 20250729 135549pnq
2025-07-29 14:05:45,900 - EnvironmentService - INFO -   环境 4: mentst
2025-07-29 14:05:45,900 - EnvironmentService - INFO -   环境 5: ort 20250729 132447md
2025-07-29 14:05:45,900 - EnvironmentService - INFO -   环境 6: ee
2025-07-29 14:05:45,900 - EnvironmentService - INFO -   环境 7: ee
2025-07-29 14:05:45,901 - AutomationController - INFO - SUCCESS: ✓ 发现 7 个可用环境
2025-07-29 14:05:45,901 - AutomationController - INFO -   环境 1: a (scanning)
2025-07-29 14:05:45,901 - AutomationController - INFO -   环境 2: Be (scanning)
2025-07-29 14:05:45,902 - AutomationController - INFO -   环境 3: fullscreen 20250729 135549pnq (scanning)
2025-07-29 14:05:45,902 - AutomationController - INFO -   环境 4: mentst (scanning)
2025-07-29 14:05:45,902 - AutomationController - INFO -   环境 5: ort 20250729 132447md (scanning)
2025-07-29 14:05:45,903 - AutomationController - INFO -   环境 6: ee (scanning)
2025-07-29 14:05:45,903 - AutomationController - INFO -   环境 7: ee (scanning)
2025-07-29 14:05:45,903 - AutomationController - INFO - [文件扫描] 扫描视频目录: videos
2025-07-29 14:05:45,904 - AutomationController - INFO -   ✓ test_video.mp4 (0.0MB)
2025-07-29 14:05:45,904 - AutomationController - INFO - SUCCESS: 找到 1 个有效视频文件
2025-07-29 14:05:45,905 - AutomationController - INFO - [批量上传] 开始批量上传 1 个视频...
2025-07-29 14:05:45,905 - AutomationController - INFO - ==================================================
2025-07-29 14:05:45,905 - AutomationController - INFO - 📹 上传视频 1/1
2025-07-29 14:05:45,906 - AutomationController - INFO - 视频: test_video.mp4
2025-07-29 14:05:45,906 - AutomationController - INFO - 环境: a
2025-07-29 14:05:45,906 - AutomationController - INFO - ==================================================
2025-07-29 14:05:45,906 - AutomationController - INFO - [环境启动] 打开环境: a
2025-07-29 14:05:45,907 - EnvironmentService - INFO - [打开环境] 正在打开环境: a
2025-07-29 14:05:45,907 - WindowService - INFO - 查找Hub Studio窗口...
2025-07-29 14:05:45,908 - WindowService - WARNING - 未找到Hub Studio窗口，但继续执行
2025-07-29 14:05:45,908 - EnvironmentService - WARNING - 无法激活Hub Studio窗口，继续执行...
2025-07-29 14:05:45,909 - EnvironmentService - INFO - 点击环境位置: (225, 200)
2025-07-29 14:05:48,418 - EnvironmentService - INFO - 等待浏览器窗口启动...
2025-07-29 14:05:48,420 - WindowService - INFO - 等待新浏览器窗口出现...
2025-07-29 14:06:03,434 - WindowService - WARNING - 等待新浏览器窗口超时
2025-07-29 14:06:03,435 - EnvironmentService - WARNING - 未检测到新浏览器窗口，但继续执行流程
2025-07-29 14:06:03,437 - AutomationController - INFO - [页面导航] 跳转到YouTube Studio
2025-07-29 14:06:03,437 - YouTubeService - INFO - [YouTube导航] 正在跳转到YouTube Studio...
2025-07-29 14:06:03,438 - BrowserService - WARNING - 未找到浏览器窗口，等待3秒后重试...
2025-07-29 14:06:06,440 - BrowserService - WARNING - 仍未找到浏览器窗口，但继续执行...
2025-07-29 14:06:06,441 - BrowserService - INFO - 导航到: https://studio.youtube.com
2025-07-29 14:06:06,442 - BrowserService - WARNING - 未找到浏览器窗口，等待3秒后重试...
2025-07-29 14:06:09,445 - BrowserService - WARNING - 仍未找到浏览器窗口，但继续执行...
2025-07-29 14:06:28,020 - BrowserService - INFO - SUCCESS: 已导航到: https://studio.youtube.com
2025-07-29 14:06:28,021 - BrowserService - INFO - 等待页面加载完成 (超时: 30秒)...
2025-07-29 14:06:34,023 - BrowserService - INFO - SUCCESS: 页面加载完成
2025-07-29 14:06:34,024 - YouTubeService - INFO - 等待YouTube Studio页面加载...
2025-07-29 14:07:06,308 - YouTubeService - INFO - 等待10秒后，假设页面已加载完成
2025-07-29 14:07:06,308 - AutomationController - INFO - [上传启动] 启动视频上传流程
2025-07-29 14:07:06,309 - YouTubeService - INFO - [上传准备] 开始视频上传...
2025-07-29 14:07:06,310 - BrowserService - WARNING - 未找到浏览器窗口，等待3秒后重试...
2025-07-29 14:07:09,311 - BrowserService - WARNING - 仍未找到浏览器窗口，但继续执行...
2025-07-29 14:07:20,343 - YouTubeService - WARNING - 未找到创建按钮，尝试直接上传...
2025-07-29 14:07:20,344 - YouTubeService - INFO - 模拟视频上传流程...
2025-07-29 14:07:23,345 - AutomationController - INFO - [文件选择] 选择视频文件
2025-07-29 14:07:23,346 - YouTubeService - INFO - [文件选择] 选择视频文件: test_video.mp4
2025-07-29 14:07:39,640 - YouTubeService - INFO - 尝试使用快捷键打开文件选择
2025-07-29 14:07:42,145 - YouTubeService - INFO - 在文件对话框中输入路径
2025-07-29 14:07:47,792 - YouTubeService - INFO - 等待文件上传开始...
2025-07-29 14:07:52,793 - YouTubeService - INFO - SUCCESS: 视频文件选择完成
2025-07-29 14:07:52,794 - AutomationController - INFO - [信息填写] 填写视频信息: test_video
2025-07-29 14:07:52,794 - YouTubeService - INFO - [信息填写] 填写视频信息...
2025-07-29 14:08:04,500 - YouTubeService - ERROR - 未找到标题输入框
2025-07-29 14:08:04,501 - YouTubeService - WARNING - 标题填写失败，但继续执行...
2025-07-29 14:08:04,501 - YouTubeService - INFO - SUCCESS: 视频信息填写完成
2025-07-29 14:08:04,502 - AutomationController - INFO - [视频发布] 发布视频
2025-07-29 14:08:04,502 - YouTubeService - INFO - [视频发布] 发布视频...
2025-07-29 14:08:13,171 - YouTubeService - WARNING - 未找到发布按钮，模拟发布流程...
2025-07-29 14:08:13,673 - YouTubeService - INFO - 等待视频发布完成...
2025-07-29 14:08:23,675 - YouTubeService - INFO - SUCCESS: 视频发布流程完成
2025-07-29 14:08:23,675 - AutomationController - INFO - SUCCESS: ✓ 视频 1 上传成功
2025-07-29 14:08:23,676 - AutomationController - INFO - ============================================================
2025-07-29 14:08:23,677 - AutomationController - INFO - 📊 批量上传完成
2025-07-29 14:08:23,677 - AutomationController - INFO - 成功: 1
2025-07-29 14:08:23,678 - AutomationController - INFO - 失败: 0
2025-07-29 14:08:23,678 - AutomationController - INFO - 总计: 1
2025-07-29 14:08:23,678 - AutomationController - INFO - 成功率: 100.0%
2025-07-29 14:08:23,679 - AutomationController - INFO - ============================================================
